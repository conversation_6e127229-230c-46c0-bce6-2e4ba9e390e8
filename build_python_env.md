# OCRAutoScore Docker 使用指南

## 1. 环境要求

- Docker 19.03 或以上
- NVIDIA Container Toolkit (如需 GPU 支持)
- CUDA 11.0 或以上 (如需 GPU 支持)

## 2. 镜像信息

**当前可用镜像**：
- `jxjyzzc/ocr-autoscore:latest` (19.2GB)
- 包含预下载的 HuggingFace 模型
- 基于 Python 3.6 + 完整依赖环境

## 3. 快速开始

### 3.1 拉取镜像（推荐）

```bash
# 从 DockerHub 拉取预构建镜像
docker pull jxjyzzc/ocr-autoscore:latest
```

### 3.2 本地构建（可选）

如需自定义构建：
```bash
# 进入build目录
cd build

# 构建镜像
docker build -f Dockerfile -t jxjyzzc/ocr-autoscore:latest .
```

## 4. 测试 score.py 功能

### 4.1 快速测试
```bash
# 运行测试脚本
docker run --rm -it \
    -v $(pwd):/workspace \
    jxjyzzc/ocr-autoscore:latest \
    bash -c "cd /workspace && python score.py"
```

### 4.2 交互式测试
```bash
# 启动交互式容器
docker run --rm -it \
    -v $(pwd):/workspace \
    jxjyzzc/ocr-autoscore:latest \
    bash

# 在容器内运行
cd /workspace
python score.py
```

## 5. 常用操作

### 5.1 启动 Django 服务
```bash
docker run -d \
    --name ocr-autoscore \
    -p 8000:8000 \
    -v $(pwd)/data:/app/data \
    jxjyzzc/ocr-autoscore:latest
```

### 5.2 启动容器（不自动运行服务）
```bash
docker run -d \
    --name ocr-autoscore \
    --entrypoint="" \
    -p 8000:8000 \
    -v $(pwd)/data:/app/data \
    jxjyzzc/ocr-autoscore:latest \
    tail -f /dev/null
```

### 5.3 进入容器
```bash
docker exec -it ocr-autoscore bash
```

### 5.4 查看日志
```bash
docker logs -f ocr-autoscore
```

### 5.5 停止和删除容器
```bash
docker stop ocr-autoscore
docker rm ocr-autoscore
```

## 6. 运行单字符识别脚本
```bash
# 方式1：直接运行
docker run --rm -v $(pwd):/workspace jxjyzzc/ocr-autoscore:latest \
    python /workspace/scoreblocks/singleCharacterRecognition.py \
    --image_path /workspace/example_img/test.jpg \
    --model_type WaveMix

# 方式2：进入容器后运行
docker exec -it ocr-autoscore bash
cd /workspace
python scoreblocks/singleCharacterRecognition.py \
    --image_path ./example_img/test.jpg \
    --model_type SpinalVGG
```

参数说明：
- --image_path: (必须)输入图片路径
- --model_type: (可选)模型类型，支持WaveMix或SpinalVGG，默认WaveMix

## 7. 故障排除

### 7.1 模型下载问题
如果遇到 HuggingFace 模型下载失败：
```bash
# 在容器内手动下载
docker exec -it ocr-autoscore bash
export HF_ENDPOINT=https://hf-mirror.com
python -c "from transformers import AutoModel; AutoModel.from_pretrained('openai/clip-vit-large-patch14')"
```

### 7.2 GPU 支持
如需 GPU 支持，启动时添加 `--gpus all` 参数：
```bash
docker run --gpus all -d --name ocr-autoscore jxjyzzc/ocr-autoscore:latest
```

### 7.3 内存不足
如果遇到内存不足，可以增加 Docker 内存限制：
```bash
docker run --memory=8g -d --name ocr-autoscore jxjyzzc/ocr-autoscore:latest
```

## 8. 版本信息

**当前镜像版本**：
- 镜像：jxjyzzc/ocr-autoscore:latest
- Python: 3.6
- PaddleOCR: 2.0.1
- PaddlePaddle: 2.4.2
- PyTorch: 1.10.1
- Transformers: 4.12.0
- Ultralytics: 8.0.208

**预下载模型**：
- openai/clip-vit-large-patch14
- microsoft/deberta-v3-base
- bert-base-uncased
