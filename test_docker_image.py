#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试脚本，验证 Docker 镜像的基本功能
"""

def test_imports():
    """测试主要模块的导入"""
    print("=== 测试模块导入 ===")
    
    try:
        import cv2
        print("✅ OpenCV 导入成功，版本:", cv2.__version__)
    except ImportError as e:
        print("❌ OpenCV 导入失败:", e)
    
    try:
        import paddleocr
        print("✅ PaddleOCR 导入成功")
    except ImportError as e:
        print("❌ PaddleOCR 导入失败:", e)
    
    try:
        import torch
        print("✅ PyTorch 导入成功，版本:", torch.__version__)
        print("   CUDA 可用:", torch.cuda.is_available())
    except ImportError as e:
        print("❌ PyTorch 导入失败:", e)
    
    try:
        import transformers
        print("✅ Transformers 导入成功，版本:", transformers.__version__)
    except ImportError as e:
        print("❌ Transformers 导入失败:", e)
    
    try:
        import ultralytics
        print("✅ Ultralytics 导入成功，版本:", ultralytics.__version__)
    except ImportError as e:
        print("❌ Ultralytics 导入失败:", e)

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    # 测试 OpenCV
    try:
        import cv2
        import numpy as np
        
        # 创建一个简单的测试图像
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        cv2.rectangle(test_img, (10, 10), (90, 90), (255, 255, 255), -1)
        
        # 测试图像处理
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        print("✅ OpenCV 图像处理功能正常")
        
    except Exception as e:
        print("❌ OpenCV 功能测试失败:", e)
    
    # 测试 PyTorch
    try:
        import torch
        
        # 创建一个简单的张量
        x = torch.randn(2, 3)
        y = torch.randn(3, 2)
        z = torch.mm(x, y)
        print("✅ PyTorch 基本运算功能正常")
        
    except Exception as e:
        print("❌ PyTorch 功能测试失败:", e)

def test_model_cache():
    """测试预下载的模型缓存"""
    print("\n=== 测试模型缓存 ===")
    
    import os
    cache_dir = "/root/.cache/huggingface/hub"
    
    if os.path.exists(cache_dir):
        print("✅ HuggingFace 缓存目录存在")
        
        # 检查预下载的模型
        models = [
            "clip-vit-large-patch14",
            "deberta-v3-base", 
            "bert-base-uncased"
        ]
        
        for model in models:
            model_path = os.path.join(cache_dir, model)
            if os.path.exists(model_path):
                print(f"✅ 模型 {model} 已缓存")
            else:
                print(f"❌ 模型 {model} 未找到")
    else:
        print("❌ HuggingFace 缓存目录不存在")

def test_project_structure():
    """测试项目结构"""
    print("\n=== 测试项目结构 ===")
    
    import os
    
    # 检查主要目录和文件
    paths_to_check = [
        "/workspace/score.py",
        "/workspace/segmentation",
        "/workspace/scoreblocks",
        "/workspace/example_img"
    ]
    
    for path in paths_to_check:
        if os.path.exists(path):
            print(f"✅ {path} 存在")
        else:
            print(f"❌ {path} 不存在")

if __name__ == "__main__":
    print("Docker 镜像功能测试")
    print("=" * 50)
    
    test_imports()
    test_basic_functionality()
    test_model_cache()
    test_project_structure()
    
    print("\n" + "=" * 50)
    print("测试完成！")
