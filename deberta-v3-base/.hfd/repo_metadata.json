{"_id": "621ffdc136468d709f17dee4", "id": "microsoft/deberta-v3-base", "private": false, "pipeline_tag": "fill-mask", "library_name": "transformers", "tags": ["transformers", "pytorch", "tf", "rust", "deberta-v2", "deberta", "deberta-v3", "fill-mask", "en", "arxiv:2006.03654", "arxiv:2111.09543", "license:mit", "endpoints_compatible", "region:us"], "downloads": 2152183, "likes": 328, "modelId": "microsoft/deberta-v3-base", "author": "microsoft", "sha": "8ccc9b6f36199bec6961081d44eb72fb3f7353f3", "lastModified": "2022-09-22T12:34:19.000Z", "gated": false, "disabled": false, "mask_token": "[MASK]", "widgetData": [{"text": "Paris is the [MASK] of France."}, {"text": "The goal of life is [MASK]."}], "model-index": null, "config": {"model_type": "deberta-v2", "tokenizer_config": {}}, "cardData": {"language": "en", "tags": ["deberta", "deberta-v3", "fill-mask"], "thumbnail": "https://huggingface.co/front/thumbnails/microsoft.png", "license": "mit"}, "transformersInfo": {"auto_model": "AutoModel"}, "siblings": [{"rfilename": ".gitattributes"}, {"rfilename": "README.md"}, {"rfilename": "config.json"}, {"rfilename": "pytorch_model.bin"}, {"rfilename": "rust_model.ot"}, {"rfilename": "spm.model"}, {"rfilename": "tf_model.h5"}, {"rfilename": "tokenizer_config.json"}], "spaces": ["ruanchaves/portuguese-offensive-language-detection", "manu/gliner_multi", "anonymous8/Rapid-Textual-Adversarial-Defense", "Tonic/gliner_base", "wandb/guardrails-genie", "jmaciejowski/live_stock_news_dashboard", "ruanchaves/portuguese-question-answering", "riccorl/relik-entity-linking", "non2013/SincereQuestions", "NithitEiEi/insincere-question", "KavishNayeem/PhishPatrol", "njmery/microsoft-deberta-v3-base", "ruanchaves/portuguese-semantic-similarity", "ruanchaves/portuguese-textual-entailment", "ruanchaves/portuguese-text-simplification", "Yuichiroh/UOT", "kdevoe/testspace", "ieuniversity/live_stock_news_dashboard", "peter2000/gliner_multi", "kaitehtzeng/primary_app", "CarlosMalaga/3ie-intervention-outcome-entity-linking", "rbiswasfc/compare-tokenizers", "projecte-aina/multiner_demo", "PFEemp2024/DCWIR-Demo", "shoukaku/fake-health-news-detection", "PFEemp2024/DCWIR-Offcial-Demo", "srivelan/microsoft-deberta-v3-base", "Anamta98/microsoft-deberta-v3-base", "WebashalarForML/ImageDataExtractor2", "WebashalarForML/ImageDataExtractor3", "ahmedovv/microsoft-deberta-v3-base", "max-bevza/ViralTweets", "Chamin09/sustainable_content_moderator", "asad231/FakeNews_DeepFakeDetectionPlan", "Muzammil-Mj/fakeNews-DeepFakesDetection", "asad231/FakeNews_DeepFakeDetectionPlan1", "kira<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/FakeNews_Deepfake_Detection", "aravindbethapudi2017/mbti", "Tachygraphy-Microtext-Normalization-IEMK25/Tachygraphy-Microtext-Analysis-and-Normalization-ArchismanCoder", "novikov-ie/arxiv-classifier", "dine24/luxury-decor-rag", "bvd757/Papers_classification", "nickusan/NlpDev", "b3nchMark/ysda_ml_2_hf_tune", "ssbars/ysdaml4", "coldn00dl3s/llm-human-prediction-demo", "darshil<PERSON><PERSON>h/span-extraction", "amjad21sw18/AIHealthcareStatusChecker", "rshakked/safe-talk", "jsasportas/microsoft-deberta-v3-base", "attention-conll/visualizer"], "createdAt": "2022-03-02T23:29:05.000Z", "inference": "warm", "usedStorage": **********}