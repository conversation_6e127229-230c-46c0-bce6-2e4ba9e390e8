# MSPLM 模型训练指南

## 概述

MSPLM (Multi-Scale Pre-trained Language Model) 是基于 NAACL 2022 论文 "On the Use of BERT for Automated Essay Scoring: Joint Learning of Multi-Scale Essay Representation" 开发的作文自动评分模型。

## 模型架构

- **预训练模型**: DeBERTaV3-large
- **评分方式**: 回归预测 (0-12分)
- **训练策略**: 五折交叉验证
- **损失函数**: 多重损失 (MSE + RankLoss + CosLoss)

## 数据准备

### 1. ASAP数据集

数据集应放置在 `./asap/` 目录下，包含以下文件：

```
asap/
├── training_set_rel3.tsv          # 训练数据
├── valid_set.tsv                   # 验证数据
├── test_set.tsv                    # 测试数据
└── pkl/                            # 预处理后的数据
    └── train/
        ├── p1_dataset.pkl          # Prompt 1 数据
        ├── p2_dataset.pkl          # Prompt 2 数据
        └── ...
```

### 2. 数据格式

ASAP数据集包含8个不同的写作提示(prompt)：
- **Prompt 1**: 计算机对人们生活的影响 (议论文)
- **Prompt 2**: 审查制度 (议论文)
- **Prompt 3-8**: 其他主题的作文

每个prompt的作文评分范围不同，模型会自动进行分数标准化。

## 训练配置

### 配置文件

训练参数在 `ini/` 目录下的配置文件中定义：

```ini
# ini/p1.ini - Prompt 1 的训练配置
[p1]
prompt = p1
plm = microsoft/deberta-v3-large
batch_size = 4
plm_batch_size = 2
train_epoch = 20
lr_0 = 2e-5
lr_1 = 1e-4
w1 = 0.2
w2 = 0.4
w3 = 0.4
chunk_sizes = 0
r_dropout = 0.1
data_sample_rate = 1.0
```

### 关键参数说明

- `plm`: 预训练语言模型路径
- `batch_size`: 训练批次大小
- `train_epoch`: 训练轮数
- `lr_0`, `lr_1`: 不同组件的学习率
- `w1`, `w2`, `w3`: 多重损失函数的权重
- `chunk_sizes`: 多尺度分块大小 (0表示不使用分块)

## 训练流程

### 1. 环境准备

```bash
# 安装依赖
pip install torch transformers pandas scikit-learn
pip install configargparse matplotlib
```

### 2. 数据预处理

```bash
# 运行数据预处理脚本
cd asap
python makedataset.py
```

### 3. 开始训练

```bash
# 训练 Prompt 1 模型 (本地环境)
python train.py

# 使用Docker容器训练 (bert-base-uncased模型)
docker exec -it ocr-autoscore bash -c "cd /app/scoreblocks/MSPLM && python train.py --prompt p1 --PLM bert-base-uncased --train_epoch 10 --batch_size 32"

```

# 参数说明
| 参数 | 说明 | 示例值 |
|------|------|--------|
| --prompt | 训练使用的prompt编号 | p1 |
| --PLM | 预训练语言模型名称 | bert-base-uncased |
| --train_epoch | 训练轮数 | 10 |
| --batch_size | 训练批次大小 | 32 |

### 4. 训练过程

训练采用五折交叉验证：

```
数据分割:
- 5折数据，每次选择不同的验证集和测试集
- 例如: val0test1 表示第0折作验证集，第1折作测试集
- 其余3折作为训练集

训练循环:
for epoch in range(train_epoch):
    1. 前向传播计算损失
    2. 反向传播更新参数
    3. 在验证集上评估性能
    4. 如果验证集QWK提升，保存模型
```

### 5. 模型保存

训练过程中，当验证集上的QWK (Quadratic Weighted Kappa) 达到最佳时，模型会自动保存：

```
prediction/
└── p1/                             # Prompt 1
    ├── val0test1/                  # 验证集0，测试集1
    │   ├── best_total.bin          # 最佳完整模型权重
    │   ├── best_chunk.bin          # 最佳分块模型权重
    │   ├── best_epoch.txt          # 最佳轮次信息
    │   ├── train/                  # 训练集预测结果
    │   ├── val/                    # 验证集预测结果
    │   └── test/                   # 测试集预测结果
    ├── val0test2/
    └── ...
```

## 模型评估

### 评估指标

- **QWK (Quadratic Weighted Kappa)**: 主要评估指标
- **Pearson相关系数**: 线性相关性
- **MSE (均方误差)**: 预测误差

### 性能监控

训练过程中会生成性能曲线图，保存在对应的fold目录下。

## 使用训练好的模型

### 1. 模型加载

```python
from scoreblocks.essayscoremodel import model

# 初始化模型（自动加载最佳权重）
essay_scorer = model()

# 对作文进行评分
essays = ["This is a sample essay about computers..."]
scores = essay_scorer.getscore(essays)
print(f"Essay score: {scores[0]:.2f}/12")
```

### 2. 路径配置

如需使用不同的模型权重，修改 `essayscoremodel.py` 中的模型路径：

```python
# 当前默认路径
model_path = './MSPLM/prediction/p1/val0test1/best_total.bin'

# 可修改为其他fold的模型
model_path = './MSPLM/prediction/p1/val1test2/best_total.bin'
```

## 训练不同Prompt

### 1. 修改配置文件

复制 `ini/p1.ini` 为 `ini/p2.ini`，修改prompt参数：

```ini
[p2]
prompt = p2
# 其他参数保持不变或根据需要调整
```

### 2. 修改训练脚本

在 `train.py` 中修改配置文件路径：

```python
# 原来
p = configargparse.ArgParser(default_config_files=["ini/p1.ini"])

# 修改为
p = configargparse.ArgParser(default_config_files=["ini/p2.ini"])
```

### 3. 运行训练

```bash
python train.py
```

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小 `batch_size` 和 `plm_batch_size`
   - 使用梯度累积

2. **模型文件不存在**
   - 确保已完成训练
   - 检查模型保存路径

3. **数据集格式错误**
   - 确保ASAP数据集格式正确
   - 检查编码格式 (UTF-8)

4. **依赖库版本冲突**
   - 使用虚拟环境
   - 安装指定版本的依赖

### 调试建议

1. 先在小数据集上测试
2. 监控训练损失和验证指标
3. 检查模型输出的分数范围
4. 对比不同fold的性能

## 进阶优化

### 1. 超参数调优

- 学习率调度
- 损失函数权重
- Dropout比率
- 训练轮数

### 2. 模型改进

- 尝试不同的预训练模型
- 调整多尺度分块策略
- 实验不同的损失函数组合

### 3. 数据增强

- 同义词替换
- 句子重排
- 回译技术

## 参考资料

- [原论文](https://aclanthology.org/2022.naacl-main.249/)
- [原始代码库](https://github.com/lingochamp/Multi-Scale-BERT-AES)
- [MSPLM改进版本](https://github.com/vkgo/MSPLM)
- [ASAP数据集](https://www.kaggle.com/c/asap-aes)