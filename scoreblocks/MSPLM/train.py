import pickle
from asap.makedataset import Dataset
import torch
import configargparse
from model import AESmodel
from fivefold import fivefold
import os

def _initialize_arguments(p: configargparse.ArgParser):
    """
    初始化训练参数
    
    参数说明:
    - bert_model_path: BERT模型路径 (已弃用，现使用PLM参数)
    - efl_encode: 是否继续训练 (布尔值)
    - r_dropout: 回归层的dropout比率，防止过拟合
    - batch_size: 训练批次大小，影响内存使用和训练稳定性
    - plm_batch_size: 预训练语言模型的批次大小
    - cuda: 是否使用GPU加速训练
    - device: 计算设备 (cuda/cpu)
    - test_file: 测试数据文件路径
    - data_sample_rate: 数据采样率，用于快速实验 (1.0表示使用全部数据)
    - prompt: 作文提示编号 (p1-p8)
    - chunk_sizes: 多尺度分块大小，0表示不使用分块
    - train_epoch: 训练轮数
    - lr_0, lr_1: 不同模型组件的学习率
    - w1, w2, w3: 多重损失函数的权重系数
    - PLM: 预训练语言模型名称或路径
    """
    p.add('--bert_model_path', help='bert_model_path (deprecated, use PLM instead)')
    p.add('--efl_encode', action='store_true', help='is continue training')
    p.add('--r_dropout', help='regression dropout rate', type=float)
    p.add('--batch_size', help='training batch size', type=int)
    p.add('--plm_batch_size', help='PLM batch size', type=int)
    p.add('--cuda', action='store_true', help='use gpu or not')
    p.add('--device', help='computing device')
    p.add('--test_file', help='test data file')
    p.add('--data_sample_rate', help='data sampling rate (1.0 for full data)', type=float)
    p.add('--prompt', help='essay prompt (p1-p8)')
    p.add('--chunk_sizes', help='multi-scale chunk sizes (0 for no chunking)', type=str)
    p.add('--train_epoch', help='number of training epochs', type=int)
    p.add('--lr_0', help='learning rate for main model', type=float)
    p.add('--lr_1', help='learning rate for chunk model', type=float)
    p.add('--w1', help='weight for MSE loss', type=float)
    p.add('--w2', help='weight for rank loss', type=float)
    p.add('--w3', help='weight for cosine loss', type=float)
    p.add('--PLM', help='pre-trained language model name/path', type=str)

    args = p.parse_args()

    # 自动检测并设置计算设备
    if torch.cuda.is_available() and args.cuda:
        args.device = 'cuda'
        print(f"Using GPU: {torch.cuda.get_device_name()}")
    else:
        args.device = 'cpu'
        print("Using CPU")
    return args


if __name__ == "__main__":
    """
    MSPLM模型训练主程序
    
    训练流程:
    1. 加载配置参数和数据集
    2. 进行五折交叉验证训练
    3. 为每个fold组合训练独立的模型
    4. 保存最佳模型权重和预测结果
    
    五折交叉验证说明:
    - 将数据分为5折
    - 每次选择不同的验证集(val_index)和测试集(test_index)
    - 剩余3折作为训练集
    - 总共训练5×4=20个模型 (排除val_index==test_index的情况)
    """
    
    # 初始化训练参数
    # 默认使用p1.ini配置文件，可修改为其他prompt的配置
    p = configargparse.ArgParser(default_config_files=["ini/p1.ini"])
    args = _initialize_arguments(p)
    print(f'Training Configuration:')
    print(f'Device: {args.device}')
    print(f'PyTorch Version: {torch.__version__}')
    print(f'Prompt: {args.prompt}')
    print(f'PLM: {args.PLM}')
    print(f'Epochs: {args.train_epoch}')
    print(f'Batch Size: {args.batch_size}')
    print('-' * 50)
    
    # 加载预处理的数据集
    # 数据集文件格式: {prompt}_dataset.pkl (如p1_dataset.pkl)
    dataset_path = f'./asap/pkl/train/{args.prompt}_dataset.pkl'
    print(f'Loading dataset from: {dataset_path}')
    
    try:
        with open(dataset_path, 'rb') as f:
            dataset = pickle.load(f)
        print(f'Dataset loaded successfully. Total samples: {len(dataset)}')
    except FileNotFoundError:
        print(f"Error: Dataset file {dataset_path} not found!")
        print("Please run 'python asap/makedataset.py' first to prepare the data.")
        exit(1)
    
    # 创建五折交叉验证数据分割
    folds = fivefold(dataset)
    print(f'Created {len(folds.essay_folds)} folds for cross-validation')
    
    # 开始五折交叉验证训练
    total_models = 0
    for val_index in range(len(folds.essay_folds)):
        for test_index in range(len(folds.essay_folds)):
            # 跳过验证集和测试集相同的情况
            if val_index == test_index:
                continue
                
            total_models += 1
            foldname = f'val{val_index}test{test_index}'
            print(f'\n=== Training Model {total_models}/20: {foldname} ===')
            
            # 初始化数据容器
            valessays = []
            valscores = []
            testessays = []
            testscores = []
            trainessays = []
            trainscores = []
            
            # 分配数据到训练/验证/测试集
            for i, (essays, scores) in enumerate(zip(folds.essay_folds, folds.score_folds)):
                if i == val_index:
                    # 第val_index折作为验证集
                    valessays = folds.essay_folds[i]
                    valscores = folds.score_folds[i]
                    print(f'Validation set: fold {i}, {len(valessays)} samples')
                elif i == test_index:
                    # 第test_index折作为测试集
                    testessays = folds.essay_folds[i]
                    testscores = folds.score_folds[i]
                    print(f'Test set: fold {i}, {len(testessays)} samples')
                else:
                    # 其余折作为训练集
                    trainessays = trainessays + folds.essay_folds[i]
                    trainscores = trainscores + folds.score_folds[i]
            
            print(f'Training set: {len(trainessays)} samples')
            
            # 创建AES模型实例
            model = AESmodel(traindata=(trainessays, trainscores), 
                           valdata=(valessays, valscores),
                           testdata=(testessays, testscores), 
                           foldname=foldname, 
                           args=args)
            
            # 创建模型保存目录
            # 目录结构: ./prediction/{prompt}/{foldname}/
            filepath = f'./prediction/{args.prompt}'
            if not os.path.isdir(filepath):
                os.makedirs(filepath)
                print(f'Created directory: {filepath}')
                
            fold_path = filepath + f'/{foldname}'
            if not os.path.isdir(fold_path):
                # 创建fold目录和子目录
                os.makedirs(fold_path)
                os.makedirs(fold_path + '/train')   # 训练集预测结果
                os.makedirs(fold_path + '/val')     # 验证集预测结果
                os.makedirs(fold_path + '/test')    # 测试集预测结果
                print(f'Created fold directory: {fold_path}')
            
            # 开始训练
            print(f'Starting training for {foldname}...')
            model.train()
            print(f'Training completed for {foldname}')
    
    print(f'\n=== All Training Completed ===')
    print(f'Total models trained: {total_models}')
    print(f'Models saved in: ./prediction/{args.prompt}/')
    print('\nTo use the trained models:')
    print('1. Update the model path in essayscoremodel.py')
    print('2. Choose the best performing fold based on validation QWK')
    print('3. Test the model with new essays')
    
    pass