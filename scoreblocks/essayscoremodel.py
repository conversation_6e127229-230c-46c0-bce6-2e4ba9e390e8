import configparser
import os

import torch
import torch.nn as nn
from transformers import AutoModel
import configargparse
from transformers import AutoTokenizer
from scoreblocks.MSPLM.encoder import encode_documents
from scoreblocks.MSPLM.plms import mainplm
from torch.cuda.amp import autocast


def _initialize_arguments(p):
    p.add_section('bert_model_path')
    p.add_section('efl_encode')
    p.add_section('r_dropout')
    p.add_section('batch_size')
    p.add_section('plm_batch_size')
    p.add_section('cuda')
    p.add_section('device')
    p.add_section('test_file')
    p.add_section('data_sample_rate')
    p.add_section('prompt')
    p.add_section('chunk_sizes')
    p.add_section('train_epoch')
    p.add_section('lr_0')
    p.add_section('lr_1')
    p.add_section('w1')
    p.add_section('w2')
    p.add_section('w3')
    p.add_section('PLM')

    # p.add('--bert_model_path', help='bert_model_path')
    # p.add('--efl_encode', action='store_true', help='is continue training')
    # p.add('--r_dropout', help='r_dropout', type=float)
    # p.add('--batch_size', help='batch_size', type=int)
    # p.add('--plm_batch_size', help='plm_batch_size', type=int)
    # p.add('--cuda', action='store_true', help='use gpu or not')
    # p.add('--device')
    # p.add('--test_file', help='test data file')
    # p.add('--data_sample_rate', help='data_sample_rate', type=float)
    # p.add('--prompt', help='prompt')
    # p.add('--chunk_sizes', help='chunk_sizes', type=str)
    # p.add('--train_epoch', help='train_epoch', type=int)
    # p.add('--lr_0', help='lr_0', type=float)
    # p.add('--lr_1', help='lr_1', type=float)
    # p.add('--w1', help='w1', type=float)
    # p.add('--w2', help='w2', type=float)
    # p.add('--w3', help='w3', type=float)
    # p.add('--PLM', help='PLM', type=str)

    # args = p.parse_args()

    # 将ConfigParser对象转换为字典
    config_dict = {}
    for section in p.sections():
        section_dict = {}
        for option in p.options(section):
            section_dict[option] = p.get(section, option)
        config_dict[section] = section_dict
    config_dict = config_dict['p1']
    print(config_dict)

    if torch.cuda.is_available() and config_dict['cuda']:
        config_dict['device'] = 'cuda'
    else:
        config_dict['device'] = 'cpu'
    return config_dict

def init_weights(m):
    if isinstance(m, nn.Linear):
        torch.nn.init.xavier_uniform(m.weight)
        m.bias.data.fill_(7)
class model:
    def __init__(self):
        """
        初始化作文评分模型 (MSPLM - Multi-Scale Pre-trained Language Model)
        
        该模型基于NAACL 2022论文"On the Use of BERT for Automated Essay Scoring: Joint Learning of Multi-Scale Essay Representation"
        使用DeBERTaV3-large作为预训练语言模型，结合多尺度表示学习和多重损失函数进行作文自动评分。
        
        模型训练流程:
        1. 数据准备: 使用ASAP数据集，包含8个不同prompt的作文数据
        2. 五折交叉验证: 将数据分为5折，每次用不同的验证集和测试集组合
        3. 模型训练: 使用多重损失函数(MSE + RankLoss + CosLoss)进行训练
        4. 模型保存: 当验证集QWK(Quadratic Weighted Kappa)达到最佳时保存模型
        
        模型文件路径说明:
        - 训练时模型保存在: ./MSPLM/prediction/p{prompt}/val{val_index}test{test_index}/best_total.bin
        - 其中prompt表示作文题目编号(1-8), val_index和test_index表示交叉验证的折数
        - 当前加载的是p1(prompt 1)的val0test1组合训练出的最佳模型
        
        如需训练新模型:
        1. 准备ASAP数据集放在./MSPLM/asap/目录下
        2. 运行 python ./MSPLM/train.py 开始训练
        3. 训练完成后模型会自动保存在prediction目录下
        """
        # initialize arguments
        #p = configargparse.ArgParser(default_config_files=["../scoreblocks/MSPLM/ini/p1.ini"])
        # 创建ConfigParser对象
        p = configparser.ConfigParser()
        # 保持原始参数名大小写
        p.optionxform = str
        # 读取ini文件
        # 获取当前文件的目录作为基础路径
        base_dir = os.path.dirname(os.path.abspath(__file__))
        # 构建配置文件的绝对路径
        # p1.ini包含了prompt 1的训练配置参数，如学习率、批次大小、模型路径等
        ini_path = os.path.normpath(os.path.join(base_dir, './MSPLM/ini/p1.ini'))
        p.read(ini_path, encoding='utf-8')
        self.args = _initialize_arguments(p)
        print(f"device:{self.args['device']} torch_version:{torch.__version__}")

        # if args is not None:
        #     self.args = vars(args)

        # 加载DeBERTaV3分词器
        # 定义可能的模型路径
        model_paths = [
            "/root/.cache/huggingface/hub/deberta-v3-base",  # hfd 下载路径
            "/root/.cache/huggingface/transformers",  # transformers 缓存路径
            self.args['PLM']  # 在线模型名称（作为最后的备选）
        ]

        tokenizer_loaded = False
        for model_path in model_paths:
            try:
                print(f"[INFO] 尝试从路径加载分词器: {model_path}")

                # 检查是否是本地路径且存在
                if os.path.exists(model_path):
                    print(f"[INFO] 找到本地分词器路径: {model_path}")
                    # 检查必要文件是否存在
                    required_files = ['config.json', 'tokenizer_config.json']
                    missing_files = [f for f in required_files if not os.path.exists(os.path.join(model_path, f))]
                    if missing_files:
                        print(f"[INFO] 缺少必要文件: {missing_files}，跳过此路径")
                        continue

                    # 加载分词器
                    self.tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True)
                    print(f"[INFO] ✅ 分词器从本地路径加载成功")
                    tokenizer_loaded = True
                    break
                else:
                    # 尝试在线加载（仅作为最后备选）
                    if model_path == self.args['PLM']:
                        print(f"[INFO] 尝试在线加载分词器: {model_path}")
                        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
                        print(f"[INFO] ✅ 分词器在线加载成功")
                        tokenizer_loaded = True
                        break
                    else:
                        print(f"[INFO] 路径不存在: {model_path}")
                        continue

            except Exception as e:
                print(f"[INFO] ❌ 从路径 {model_path} 加载分词器失败: {str(e)}")
                continue

        if not tokenizer_loaded:
            print(f"[ERROR] ❌ 无法从任何路径加载分词器，使用备用分词器: bert-base-uncased")
            try:
                self.tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
                print(f"[INFO] ✅ 备用分词器加载成功")
            except Exception as e:
                raise RuntimeError(f'无法加载任何分词器: {str(e)}')
        self.prompt = int(self.args["prompt"][1])  # 提取prompt编号
        self.chunk_sizes = []
        self.bert_batch_sizes = []
        
        # 载入预训练的作文评分模型
        self.bert_regression_by_word_document = mainplm(self.args)
        
        # 加载训练好的模型权重
        # 路径说明: ./MSPLM/prediction/p1/val0test1/best_total.bin
        # - p1: 表示ASAP数据集的prompt 1 (关于计算机对人们生活影响的议论文)
        # - val0test1: 表示五折交叉验证中使用第0折作为验证集，第1折作为测试集
        # - best_total.bin: 在验证集上QWK最高时保存的完整模型权重
        # 注意: 如果该文件不存在，需要先运行训练脚本生成模型文件
        model_path = os.path.normpath(os.path.join(base_dir, './MSPLM/prediction/p1/val0test1/best_total.bin'))
        self.bert_regression_by_word_document.load_state_dict(torch.load(model_path))
        self.bert_regression_by_word_document.to(self.args['device'])
        self.bert_regression_by_word_document.eval()  # 设置为评估模式

        # these are used to plot the Training Curve Chart
        self.plt_x = []
        self.plt_train_qwk = []
        self.plt_val_qwk = []
        self.plt_test_qwk = []
        self.best_val_qwk = 0.

    def getscore(self, valdata):
        """
        对输入的作文进行自动评分
        
        参数说明:
        valdata: list of str - 待评分的作文列表，每个元素是一篇作文的文本内容
                例如: ["This is essay 1...", "This is essay 2...", "This is essay 3..."]
        
        返回值:
        list of float - 对应每篇作文的评分列表，分数范围[0, 12]
                       评分基于ASAP数据集的标准，12分为满分
        
        评分流程:
        1. 文本编码: 使用DeBERTaV3对作文文本进行编码，生成词级别和文档级别的表示
        2. 特征提取: 通过多尺度BERT模型提取作文的语义特征
        3. 回归预测: 使用训练好的回归层预测作文质量分数
        4. 分数修正: 将预测分数限制在有效范围内[0, 12]
        
        注意事项:
        - 模型针对ASAP prompt 1训练，最适合评价关于"计算机对人们生活影响"主题的议论文
        - 作文长度建议在100-600词之间，过短或过长可能影响评分准确性
        - 支持批量处理，但建议单次处理的作文数量不超过32篇以避免内存问题
        """
        with torch.no_grad():
            target_scores = None
            doctok_token_indexes, doctok_token_indexes_slicenum = encode_documents(
                valdata, self.tokenizer, max_input_length=512)
            # [document_number:144, 510times:3, 3, bert_len:512] [每document有多少510:144]
            # traindata[0] is the essays

            predictions = torch.empty((doctok_token_indexes.shape[0]))
            acculation_loss = 0.
            for i in range(0, doctok_token_indexes.shape[0], self.args['batch_size']):  # range(0, 144, 32)
                batch_doctok_token_indexes = doctok_token_indexes[i:i + self.args['batch_size']].to(
                    device=self.args['device'])
                with autocast():
                    batch_doctok_predictions = self.bert_regression_by_word_document(batch_doctok_token_indexes,
                                                                                     device=self.args['device'])
                batch_doctok_predictions = torch.squeeze(batch_doctok_predictions)

                batch_predictions = batch_doctok_predictions
                if len(batch_predictions.shape) == 0:  # 证明只有一个tensor，不构成list
                    batch_predictions = torch.tensor([batch_predictions], device=self.args['device'])

                predictions[i:i + self.args['batch_size']] = batch_predictions


            predictions = predictions.detach().numpy()

            # 批量检测predictions，查看每一个作文的得分是否在[0, 12]之间
            for i in range(len(predictions)):
                if predictions[i] < 0:
                    predictions[i] = 0
                elif predictions[i] > 12:
                    predictions[i] = 12

            return predictions


if __name__ == '__main__':
    model = model()
    text = """@PERCENT1 of people agree that computers make life less complicated. I also agree with this. Using computers teaches hand-eye coordination, gives people the ability to learn about faraway places and people, and lets people talk online with other people. I think that these are all very important. Why wouldn't you want to have strong hand-eye coordination? I think this a very important skill. Computers help teach hand-eye coordination and they keep it strong. While you're looking at the screen your hand is moving the mouse where you want it to go. Good hand-eye coordination is used for a lot of things; mostly everything. If you play some sports like baseball, hand-eye is one of the most important elements. Why not make that stronger off of the feild? Also, hand-eye can be used to @ORGANIZATION1 while taking notes. Hand-eye is involved with almost everything you do. you can't have a poor hand-eye coordination or else you won't be able to function properly. @NUM1 out of @NUM2 doctors agree that hand-eye very important for healthy living. I love to travel, but I want to know about the place I'm going to before I get on the phone to go there." said @PERSON1, a science teacher at @ORGANIZATION1. He feels the way, I'm sure, a lot of people feel. They want to know about the place they are going to and they want it to be current. The computer has plenty information about a lot of different places in the world. Some books don't offer as much information or they need to be updated. Computers are also very good for learning about other cultures and traditions. No one wants to be ignorant right? People want to know what's going on in the world quick and easy. The computer does this. I remember when I was about @NUM2, our phone broke in our house. We couldn't go out and get one right away either. The only way we were able to communicate with our family and friends was by computer. The computer made it easier to e-mail everyone and tell them why we weren't answering our house phone. This happens more often than you think. People need to communicate through computer a lot. At work, if you need to talk to an employee or co-worker and you can't leave your desk, you can just e-mail the information to them. @NUM4 out of @NUM2 employees say that it is much faster and easier to e-mail information as opposed to talking them on the phone or in person. A lot of people agree that computer make life a lot easier. Computers teach hand-eye coordination and they let you communicate with other people. The most critical reason is that computers let people learn about faraway places and people. You can make a difference in the way people feel about computers. Write to your local newspaper. It's now or never!"""
    valdata = ['我是一个好孩子', '我是一个坏孩子', text]
    print(model.getscore(valdata))