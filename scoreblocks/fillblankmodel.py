import os
import time
# 强制使用本地缓存配置
os.environ['HF_HOME'] = '/root/.cache/huggingface'
print('[INFO] 强制使用本地缓存模式')

import paddleocr
import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        self.ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang=language)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 验证镜像站配置
        print('huggingface镜像站:', file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT)
        print(f'[INFO] 当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')

        # 定义可能的模型路径
        model_paths = [
            "/root/.cache/huggingface/hub/clip-vit-large-patch14",  # hfd 下载路径
            "/root/.cache/huggingface/transformers",  # transformers 缓存路径
            "openai/clip-vit-large-patch14"  # 在线模型名称（作为最后的备选）
        ]

        # 添加重试机制加载模型
        model_loaded = False
        for model_path in model_paths:
            try:
                print(f'[INFO] 尝试从路径加载CLIP模型: {model_path}')

                # 检查是否是本地路径且存在
                if os.path.exists(model_path):
                    print(f'[INFO] 找到本地模型路径: {model_path}')
                    # 检查必要文件是否存在
                    required_files = ['config.json', 'preprocessor_config.json', 'pytorch_model.bin']
                    missing_files = [f for f in required_files if not os.path.exists(os.path.join(model_path, f))]
                    if missing_files:
                        print(f'[INFO] 缺少必要文件: {missing_files}，跳过此路径')
                        continue

                    # 加载CLIP模型和处理器
                    self.clip_model = CLIPModel.from_pretrained(model_path, local_files_only=True).to(self.device)
                    self.clip_processor = CLIPProcessor.from_pretrained(model_path, local_files_only=True)
                    print('[INFO] ✅ CLIP模型从本地路径加载成功')
                    model_loaded = True
                    break
                else:
                    # 尝试在线加载（仅作为最后备选）
                    if model_path == "openai/clip-vit-large-patch14":
                        print(f'[INFO] 尝试在线加载模型: {model_path}')
                        # 临时移除离线限制
                        os.environ.pop('TRANSFORMERS_OFFLINE', None)
                        os.environ.pop('HF_DATASETS_OFFLINE', None)
                        self.clip_model = CLIPModel.from_pretrained(model_path).to(self.device)
                        self.clip_processor = CLIPProcessor.from_pretrained(model_path)
                        print('[INFO] ✅ CLIP模型在线加载成功')
                        model_loaded = True
                        break
                    else:
                        print(f'[INFO] 路径不存在: {model_path}')
                        continue

            except Exception as e:
                print(f'[INFO] ❌ 从路径 {model_path} 加载模型失败: {str(e)}')
                continue

        if not model_loaded:
            raise RuntimeError('无法从任何路径加载CLIP模型，请检查模型文件是否正确下载')

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str}
        """
        img = np.array(_img)
        result = self.ocr.ocr(img)
        if debug:
            print(result)
        if len(result[0]) == 0:
            return None
        else:
            location = result[0][0][0]
            text = result[0][0][1][0]
            return (location, text)

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        image = _img
        inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                 "A picture with the other text"], images=image, return_tensors="pt", padding=True)
        inputs.to(self.device)

        outputs = self.clip_model(**inputs)
        logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
        probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
        if debug:
            print(probs)
        index = torch.argmax(probs, dim=1)
        return index

if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])