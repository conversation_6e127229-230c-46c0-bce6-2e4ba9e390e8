import os
import time
# 强制使用本地缓存配置
os.environ['HF_HOME'] = '/root/.cache/huggingface'
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
print('[INFO] 强制使用本地缓存模式')

import paddleocr
import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        self.ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang=language)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 验证镜像站配置
        print('huggingface镜像站:', file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT)
        print(f'[INFO] 当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')
        
        # 添加重试机制加载模型
        max_retries = 3
        for i in range(max_retries):
            try:
                print(f'[INFO] 尝试加载CLIP模型 (第{i+1}次)')
                # 使用默认缓存目录
                # 加载CLIP模型和处理器
                self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-large-patch14", local_files_only=True).to(self.device)
                self.clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-large-patch14", local_files_only=True)
                print('[INFO] ✅ CLIP模型加载成功')
                break
            except Exception as e:
                print(f'[INFO] ❌ 模型加载失败: {str(e)}')
                if i < max_retries - 1:
                    print(f'[INFO] 等待5秒后重试...')
                    time.sleep(5)
                else:
                    raise RuntimeError(f'无法加载CLIP模型: {str(e)}')

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str}
        """
        img = np.array(_img)
        result = self.ocr.ocr(img)
        if debug:
            print(result)
        if len(result[0]) == 0:
            return None
        else:
            location = result[0][0][0]
            text = result[0][0][1][0]
            return (location, text)

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        image = _img
        inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                 "A picture with the other text"], images=image, return_tensors="pt", padding=True)
        inputs.to(self.device)

        outputs = self.clip_model(**inputs)
        logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
        probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
        if debug:
            print(probs)
        index = torch.argmax(probs, dim=1)
        return index

if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])