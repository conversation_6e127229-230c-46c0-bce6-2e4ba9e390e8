# 基础镜像使用 Python 3.6
FROM python:3.6-slim

# 设置 apt 国内源（中科大）
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list \
    && sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list


# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    g++ \
    libgl1-mesa-glx \
    libpq-dev \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建必要的目录
RUN mkdir -p /app/django_server

# 复制依赖文件
COPY requirements.txt .
COPY ultralytics-8.0.208.tar.gz .

# 设置 pip 国内源（阿里云）
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

RUN pip install --upgrade pip setuptools wheel

ENV HF_ENDPOINT=https://hf-mirror.com

# 安装 Python 依赖
RUN pip install  -r requirements.txt


# 安装本地的 ultralytics 包
RUN pip install ultralytics-8.0.208.tar.gz

# 预下载 CLIP 模型
RUN export HF_ENDPOINT=https://hf-mirror.com && \
    export TRANSFORMERS_CACHE=/root/.cache/huggingface/transformers && \
    mkdir -p /root/.cache/huggingface/transformers && \
    python -c "import os; os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'; os.environ['TRANSFORMERS_CACHE'] = '/root/.cache/huggingface/transformers'; from transformers import CLIPModel, CLIPProcessor; print('Downloading CLIP model...'); model = CLIPModel.from_pretrained('openai/clip-vit-base-patch32'); processor = CLIPProcessor.from_pretrained('openai/clip-vit-base-patch32'); print('CLIP model downloaded successfully')"

# 复制应用代码 (从项目根目录复制)
COPY ../.. .


# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# 暴露端口
EXPOSE 8000


# 复制启动脚本到/opt目录 (避免运行时挂载覆盖)
COPY bash.sh /opt/
RUN test -f /opt/bash.sh && \
    chmod +x /opt/bash.sh && \
    echo "bash.sh installed successfully" || \
    (echo "Error: bash.sh not found in build context" && exit 1)

# 启动命令 (保持容器运行，可通过docker exec手动启动服务)
ENTRYPOINT ["/bin/bash", "/opt/bash.sh"]
CMD [ "executable" ]