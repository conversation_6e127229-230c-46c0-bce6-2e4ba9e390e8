# 基础镜像使用 Python 3.6
FROM python:3.6-slim

# 设置 apt 国内源（阿里云）并处理 GPG 密钥
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list \
    && sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list


# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update --allow-unauthenticated && apt-get install -y --allow-unauthenticated \
    build-essential \
    cmake \
    g++ \
    libgl1-mesa-glx \
    libpq-dev \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    wget \
    aria2 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建必要的目录
RUN mkdir -p /app/django_server

# 复制依赖文件
COPY requirements.txt .
COPY ultralytics-8.0.208.tar.gz .

# 设置 pip 国内源（阿里云）
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

RUN pip install --upgrade pip setuptools wheel

ENV HF_ENDPOINT=https://hf-mirror.com

# 安装 Python 依赖
RUN pip install  -r requirements.txt


# 安装本地的 ultralytics 包
RUN pip install ultralytics-8.0.208.tar.gz

# 安装 hfd 工具用于下载模型
RUN pip install -U hf_transfer

# 安装 hfd 工具
RUN pip install -U huggingface_hub && \
    wget -O /usr/local/bin/hfd https://hf-mirror.com/hfd/hfd.sh && \
    chmod +x /usr/local/bin/hfd

# 预下载所有必要的 HuggingFace 模型到 /root/.cache/huggingface/hub
RUN export HF_ENDPOINT=https://hf-mirror.com && \
    mkdir -p /root/.cache/huggingface/hub && \
    cd /root/.cache/huggingface/hub && \
    echo "开始下载 CLIP 模型..." && \
    hfd openai/clip-vit-large-patch14 --tool aria2c -x 4 && \
    echo "开始下载 DeBERTa 模型..." && \
    hfd microsoft/deberta-v3-base --tool aria2c -x 4 && \
    echo "开始下载 BERT 模型..." && \
    hfd bert-base-uncased --tool aria2c -x 4 && \
    echo "所有模型下载完成！" && \
    ls -la /root/.cache/huggingface/hub/

# 复制应用代码 (从项目根目录复制)
COPY ../.. .


# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# 暴露端口
EXPOSE 8000


# 复制启动脚本到/opt目录 (避免运行时挂载覆盖)
COPY bash.sh /opt/
RUN test -f /opt/bash.sh && \
    chmod +x /opt/bash.sh && \
    echo "bash.sh installed successfully" || \
    (echo "Error: bash.sh not found in build context" && exit 1)

# 启动命令 (保持容器运行，可通过docker exec手动启动服务)
ENTRYPOINT ["/bin/bash", "/opt/bash.sh"]
CMD [ "executable" ]